# Fleet Communication Debug Guide

## 🚨 **Critical Issues Fixed**

### **Issue #1: IGC Channel Mismatch** ✅ FIXED
**Problem**: Both scripts were using conflicting channel configurations
**Solution**: 
- FleetCommand now sends on `RFC_FLEET_IN` and listens on `RFC_FLEET_OUT`
- CentralCommand sends on `RFC_FLEET_OUT` and listens on `RFC_FLEET_IN`

### **Issue #2: Message Processing Logic** ✅ FIXED
**Problem**: INPUT_ORGANISER wasn't properly handling drone data format
**Solution**: Added proper validation and debug logging

### **Issue #3: Debug Logging** ✅ ADDED
**Problem**: No visibility into communication flow
**Solution**: Added comprehensive debug output to both scripts

---

## **🔧 Testing Communication Flow**

### **Step 1: Setup Test Environment**

1. **Place Required Blocks**:
   ```
   - 2 Programmable Blocks: "CENTRAL_COMMAND" and "FLEET_DRONE"
   - 1 Remote Control: "RFC_RC" (on both grids)
   - 1 Radio Antenna: Add "RFC_ANT" to Custom Data (on both grids)
   - 1 Connector: Any name (on drone grid)
   - 1 Gyro: "RFC_GYRO" (on drone grid)
   - 1 Camera: Any name (on drone grid)
   - 1 Turret: Any name (on drone grid)
   ```

2. **Install Scripts**:
   ```
   - FleetCentralCommandScript.cs → "CENTRAL_COMMAND" block
   - FleetCommandScript.cs → "FLEET_DRONE" block
   ```

### **Step 2: Initialize Systems**

1. **Start Central Command First**:
   ```
   - Run the CENTRAL_COMMAND script
   - Check output for: "Fleet Central Command Ver_008_Updated Initialized"
   - Check output for: "IGC Communication System Active"
   ```

2. **Start Fleet Drone Second**:
   ```
   - Run the FLEET_DRONE script  
   - Check output for: "Fleet Command Script Ver_008_Updated Initialized"
   - Check output for: "Listening on channel: RFC_FLEET_OUT"
   - Check output for: "Sending on channel: RFC_FLEET_IN"
   ```

### **Step 3: Monitor Communication**

**Expected Central Command Output**:
```
[DEBUG] Processing: ~RFC RUN~...
[DEBUG] Broadcasting to X drones
[IGC] Sent to fleet: BRDCST#CA0000*++++*++++*{0, 0, 0}...
```

**Expected Fleet Drone Output**:
```
[IGC] Received: BRDCST#CA0000*++++*++++*{0, 0, 0}...
[DEBUG] Processing BRDCST message
[IGC] Status update sent: #FR1234*GOTO^{100, 0, 100}...
```

### **Step 4: Verify Data Exchange**

1. **Check Central Command Custom Data**:
   ```
   - Should contain drone data entries
   - Format: #DRONE_ID*COMMAND*LOCATION*...
   ```

2. **Check Fleet Drone Behavior**:
   ```
   - Should show "System Booting" initially
   - Should process commands from central
   - Should send status updates periodically
   ```

---

## **🐛 Troubleshooting Common Issues**

### **"No IGC Messages Received"**
**Symptoms**: Scripts initialize but no communication
**Solutions**:
1. Verify both scripts are running on same grid network
2. Check IGC channel names match exactly
3. Ensure both programmable blocks are powered and owned by same player

### **"Ignoring non-BRDCST message"**
**Symptoms**: Fleet drone ignoring all messages
**Solutions**:
1. Verify Central Command is sending messages with "BRDCST" prefix
2. Check Central Command has drone data to broadcast
3. Ensure message format is correct

### **"No Drone Data in Central Command"**
**Symptoms**: Central Command shows 0 drones
**Solutions**:
1. Verify Fleet Drone is sending status updates
2. Check INPUT_ORGANISER is processing messages correctly
3. Ensure drone ID format is valid (6 characters)

### **"Communication Loop/Spam"**
**Symptoms**: Excessive message traffic
**Solutions**:
1. Verify IGC channels are correctly configured (no loops)
2. Check timing intervals in both scripts
3. Ensure PendingSend is cleared after sending

---

## **📊 Debug Output Interpretation**

### **Central Command Debug Messages**:
```
[IGC] Received from fleet: #FR1234*... → Receiving drone data
[INPUT_ORGANISER] Processing item with ID: FR1234 → Processing drone
Successfully Added New Drone FR1234 → New drone registered
[IGC] Sent to fleet: BRDCST#CA0000*... → Broadcasting commands
```

### **Fleet Drone Debug Messages**:
```
[IGC] Received: BRDCST#CA0000*... → Receiving commands
[DEBUG] Processing BRDCST message → Processing commands
[IGC] Status update sent: #FR1234*... → Sending status
```

---

## **⚡ Performance Optimization**

### **Update Frequencies**:
- Central Command: Update10 (every 10 ticks)
- Fleet Drone: Update100 (every 100 ticks)

### **Message Throttling**:
- Fleet drones send status every RUN_ASSIGNMENT seconds (3-7 seconds)
- Central Command processes one IGC message per tick

---

## **🔄 Next Steps**

1. **Test Basic Communication**: Verify message exchange works
2. **Test Command Processing**: Send GOTO commands to drones
3. **Test Fleet Coordination**: Multiple drones responding to central
4. **Test Advanced Features**: Docking, combat, formations

---

## **📞 Still Having Issues?**

If communication still doesn't work after following this guide:

1. **Check Script Compilation**: Ensure no compilation errors
2. **Verify Block Setup**: All required blocks present and named correctly
3. **Test Minimal Setup**: Start with just 2 programmable blocks
4. **Monitor Debug Output**: Use the debug messages to trace issues
5. **Check Game Version**: Ensure Space Engineers is up to date
